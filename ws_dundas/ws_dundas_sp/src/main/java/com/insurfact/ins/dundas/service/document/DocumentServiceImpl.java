package com.insurfact.ins.dundas.service.document;

import com.insurfact.ins.dundas.dto.document.DocumentDTO;
import com.insurfact.skynet.entity.StoredFile; // Assuming this entity is accessible
import com.insurfact.skynet.entity.ComplianceDocument; // Assuming this entity is accessible
import com.insurfact.skynet.entity.StoredFileDesc; // Assuming this entity is accessible
import com.insurfact.skynet.entity.Users; // Assuming this entity is accessible
import com.insurfact.skynet.entity.Contact; // Assuming this entity is accessible

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DocumentServiceImpl implements DocumentService {

    @PersistenceContext
    private EntityManager entityManager;

    private final Path fileStorageLocation;
    private final JdbcTemplate jdbcTemplate; // For StoredFileDesc BLOB access if needed

    // Constructor injection
    public DocumentServiceImpl(@Value("${file.upload-dir:./uploads}") String uploadDir, JdbcTemplate jdbcTemplate) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        this.jdbcTemplate = jdbcTemplate;
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentDTO> getDocumentsForAdvisor(Long advisorId) {
        // This implementation needs to convert advisorId (Long) to the appropriate ID type 
        // used in StoredFile (TYPE_ID which is Integer) and ComplianceDocument (OWNER which is Users object or CONTACT which is Contact object).
        // This requires fetching the Users/Contact entity for the advisor first.

        // Fetch User entity for the advisor (assuming advisorId is userIntId)
        Users advisorUser = entityManager.find(Users.class, advisorId.intValue()); // Or however you fetch Users
        Contact advisorContact = null; // Fetch contact if needed, e.g., based on user or a different ID

        List<DocumentDTO> documents = new ArrayList<>();

        // 1. Fetch from STORED_FILE
        if (advisorUser != null) {
            TypedQuery<StoredFile> sfQuery = entityManager.createQuery(
                "SELECT sf FROM StoredFile sf WHERE sf.type = :type AND sf.typeId = :typeId ORDER BY sf.creationDate DESC", StoredFile.class);
            sfQuery.setParameter("type", 11); // TYPE_ = 11 for Advisors
            sfQuery.setParameter("typeId", advisorUser.getUserIntId()); 
            List<StoredFile> storedFiles = sfQuery.getResultList();
            documents.addAll(storedFiles.stream().map(this::mapStoredFileToDTO).collect(Collectors.toList()));
        }

        // 2. Fetch from COMPLIANCE_DOCUMENT
        // Assuming advisorId can be linked via Users.userIntId to ComplianceDocument.owner (Users object)
        // Or via Contact to ComplianceDocument.contact (Contact object)
        // For simplicity, let's assume we use the Users object if available.
        if (advisorUser != null) {
            TypedQuery<ComplianceDocument> cdQuery = entityManager.createQuery(
                "SELECT cd FROM ComplianceDocument cd WHERE cd.owner = :owner ORDER BY cd.creationDate DESC", ComplianceDocument.class);
            cdQuery.setParameter("owner", advisorUser);
            List<ComplianceDocument> complianceDocuments = cdQuery.getResultList();
            documents.addAll(complianceDocuments.stream().map(this::mapComplianceDocumentToDTO).collect(Collectors.toList()));
        }
        // else if (advisorContact != null) { ... query by contact ... }
        
        // Sort all documents by date if necessary, or rely on separate query ordering
        documents.sort((d1, d2) -> d2.getUploadDate().compareTo(d1.getUploadDate()));

        return documents;
    }

    @Override
    @Transactional
    public DocumentDTO uploadDocument(Long advisorId, String description, String category, MultipartFile file) {
        // Determine userIntId for storing in StoredFile or linking ComplianceDocument
        // For simplicity, assuming advisorId is userIntId for StoredFile.TYPE_ID
        // And for ComplianceDocument, we'd need the Users entity.
        Users uploader = entityManager.find(Users.class, advisorId.intValue()); // Fetch the user
        if (uploader == null) {
            throw new RuntimeException("Uploader (advisor) not found with ID: " + advisorId);
        }

        String originalFilename = file.getOriginalFilename();
        String uniqueFilename = UUID.randomUUID().toString() + "_" + originalFilename;
        Path targetLocation = this.fileStorageLocation.resolve(uniqueFilename);

        try {
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + originalFilename + ". Please try again!", ex);
        }

        // For this example, we'll create a StoredFile record.
        // If 'category' dictates it's a ComplianceDocument, logic would differ.
        StoredFile storedFile = new StoredFile();
        storedFile.setFileName(originalFilename);
        storedFile.setFileType(file.getContentType());
        storedFile.setFileSizeKb((int) (file.getSize() / 1024));
        storedFile.setCreationDate(new Date()); // Current time
        storedFile.setLastModifDate(new Date());
        storedFile.setDescription(description);
        storedFile.setUrl("filesystem://" + uniqueFilename); // New storage strategy
        storedFile.setType(11); // Advisor documents
        storedFile.setTypeId(advisorId.intValue()); // Link to advisor
        storedFile.setUserIntId(uploader); // Link to uploader User entity
        // Set other mandatory fields as per StoredFile entity definition
        // storedFile.setSystemId("DUNDAS_SP"); // Example
        // storedFile.setCompanyId(uploader.getCompanyId()); // Example, if applicable

        entityManager.persist(storedFile);

        return mapStoredFileToDTO(storedFile);
    }

    @Override
    public Resource downloadDocument(Long documentId, String sourceTable) {
        try {
            Path filePath = null;
            if ("COMPLIANCE_DOCUMENT".equalsIgnoreCase(sourceTable)) {
                ComplianceDocument cd = entityManager.find(ComplianceDocument.class, documentId.intValue());
                if (cd == null || cd.getFilePath() == null) {
                    throw new RuntimeException("ComplianceDocument not found or filePath is null for ID: " + documentId);
                }
                // Assuming filePath is an absolute path or resolvable relative to a base
                // For new files, it would be like "filesystem://..."
                if (cd.getFilePath().startsWith("filesystem://")) {
                    filePath = this.fileStorageLocation.resolve(cd.getFilePath().substring("filesystem://".length())).normalize();
                } else {
                    // Legacy: direct path, potentially needs careful handling for security
                    filePath = Paths.get(cd.getFilePath()).normalize(); 
                }
            } else { // Default to STORED_FILE
                StoredFile sf = entityManager.find(StoredFile.class, documentId.intValue());
                if (sf == null) {
                    throw new RuntimeException("StoredFile not found with ID: " + documentId);
                }
                if (sf.getUrl() != null && sf.getUrl().startsWith("filesystem://")) {
                    filePath = this.fileStorageLocation.resolve(sf.getUrl().substring("filesystem://".length())).normalize();
                } else {
                    // Legacy BLOB access - this part needs StoredFileDesc and JdbcTemplate
                    // This is a simplified placeholder. Real BLOB access is more complex.
                    // throw new RuntimeException("Legacy BLOB download not yet fully implemented.");
                    return downloadBlobViaJdbcTemplate(sf.getStoredFileIntId());
                }
            }

            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists() && resource.isReadable()) {
                return resource;
            } else {
                throw new RuntimeException("Could not read file: " + filePath.getFileName());
            }
        } catch (MalformedURLException ex) {
            throw new RuntimeException("File not found or path is invalid", ex);
        }
    }
    
    private Resource downloadBlobViaJdbcTemplate(Integer storedFileIntId) {
        // Simplified: In a real scenario, you'd query STORED_FILE_DESC
        // for FILE_EN or FILE_FR based on language preference or availability.
        // This example assumes a method to get StoredFileDesc and then its blob.
        // You'd need a proper RowMapper or ResultSetExtractor for the BLOB.
        // For now, this is a conceptual placeholder.
        
        // StoredFileDesc desc = jdbcTemplate.queryForObject(
        // "SELECT * FROM STORED_FILE_DESC WHERE STORED_FILE_INT_ID = ?", 
        // new Object[]{storedFileIntId}, 
        // (rs, rowNum) -> { /* map to StoredFileDesc */ return new StoredFileDesc(); });
        // if (desc != null && desc.getFileEn() != null) { // or getFileFr()
        // return new ByteArrayResource(desc.getFileEn());
        // }
        throw new RuntimeException("BLOB download from STORED_FILE_DESC not fully implemented in this example.");
    }

    @Override
    @Transactional
    public void deleteDocument(Long documentId, String sourceTable) {
        if ("COMPLIANCE_DOCUMENT".equalsIgnoreCase(sourceTable)) {
            ComplianceDocument cd = entityManager.find(ComplianceDocument.class, documentId.intValue());
            if (cd != null) {
                // Delete physical file if it's in the new storage
                if (cd.getFilePath() != null && cd.getFilePath().startsWith("filesystem://")) {
                    try {
                        Path filePath = this.fileStorageLocation.resolve(cd.getFilePath().substring("filesystem://".length())).normalize();
                        Files.deleteIfExists(filePath);
                    } catch (IOException ex) {
                        // Log error, but proceed to delete DB record
                        System.err.println("Error deleting physical file: " + cd.getFilePath() + " - " + ex.getMessage());
                    }
                }
                entityManager.remove(cd);
            }
        } else { // Default to STORED_FILE
            StoredFile sf = entityManager.find(StoredFile.class, documentId.intValue());
            if (sf != null) {
                // Delete physical file if it's in the new storage
                if (sf.getUrl() != null && sf.getUrl().startsWith("filesystem://")) {
                    try {
                        Path filePath = this.fileStorageLocation.resolve(sf.getUrl().substring("filesystem://".length())).normalize();
                        Files.deleteIfExists(filePath);
                    } catch (IOException ex) {
                        System.err.println("Error deleting physical file: " + sf.getUrl() + " - " + ex.getMessage());
                    }
                }
                // Also need to delete from STORED_FILE_DESC if it was a BLOB and we want to clean up
                // jdbcTemplate.update("DELETE FROM STORED_FILE_DESC WHERE STORED_FILE_INT_ID = ?", sf.getStoredFileIntId());
                entityManager.remove(sf);
            }
        }
    }

    // --- Helper Mapper Methods ---
    private DocumentDTO mapStoredFileToDTO(StoredFile sf) {
        if (sf == null) return null;
        return DocumentDTO.builder()
            .documentId(sf.getStoredFileIntId().longValue())
            .fileName(sf.getFileName())
            .fileType(sf.getFileType())
            .fileSize(sf.getFileSizeKb() != null ? sf.getFileSizeKb().longValue() * 1024 : null)
            .uploadDate(sf.getCreationDate() != null ? sf.getCreationDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null)
            .description(sf.getDescription())
            .category(determineCategoryFromStoredFile(sf)) // Example: "General", "Profile Picture"
            .uploaderName(sf.getUserIntId() != null ? sf.getUserIntId().getDisplayName() : "Unknown") // Assuming Users entity has getDisplayName
            .accessUrl("/api/v1/documents/" + sf.getStoredFileIntId() + "?sourceTable=STORED_FILE")
            .sourceTable("STORED_FILE")
            .build();
    }

    private DocumentDTO mapComplianceDocumentToDTO(ComplianceDocument cd) {
        if (cd == null) return null;
        Path path = null;
        String fileName = "Unknown File";
        long fileSize = 0L;

        if (cd.getFilePath() != null) {
            try {
                if (cd.getFilePath().startsWith("filesystem://")) {
                     path = this.fileStorageLocation.resolve(cd.getFilePath().substring("filesystem://".length())).normalize();
                } else {
                    path = Paths.get(cd.getFilePath());
                }
                if(Files.exists(path) && Files.isReadable(path)){
                    fileName = path.getFileName().toString();
                    fileSize = Files.size(path);
                }
            } catch (Exception e) {
                // Log error or handle, could not determine file name/size from path
                System.err.println("Error accessing file for ComplianceDocument DTO: " + cd.getFilePath() + " - " + e.getMessage());
            }
        }

        return DocumentDTO.builder()
            .documentId(cd.getComplianceDocumentIntId().longValue())
            .fileName(fileName)
            .fileType(determineMimeTypeFromPath(path)) // Helper to guess MIME type
            .fileSize(fileSize)
            .uploadDate(cd.getCreationDate() != null ? cd.getCreationDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null)
            .description(cd.getDescription())
            .category("Compliance")
            .uploaderName(cd.getOwner() != null ? cd.getOwner().getDisplayName() : (cd.getContact() != null ? cd.getContact().getDisplayName() : "Unknown"))
            .accessUrl("/api/v1/documents/" + cd.getComplianceDocumentIntId() + "?sourceTable=COMPLIANCE_DOCUMENT")
            .sourceTable("COMPLIANCE_DOCUMENT")
            .build();
    }

    private String determineCategoryFromStoredFile(StoredFile sf) {
        // Add logic based on sf.getType(), sf.getSubType(), or other fields if necessary
        return "General"; 
    }

    private String determineMimeTypeFromPath(Path path) {
        if (path == null) return null;
        try {
            return Files.probeContentType(path);
        } catch (IOException e) {
            System.err.println("Could not determine MIME type for path: " + path + " - " + e.getMessage());
            return "application/octet-stream"; // Default
        }
    }
}
