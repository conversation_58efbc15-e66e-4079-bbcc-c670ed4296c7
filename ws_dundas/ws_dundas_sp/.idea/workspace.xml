<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="ws_dundas_sp:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a945d9b6-f1e7-445f-ae80-7efecb425d02" name="Changes" comment="Refactor SmokerStatus code parsing with switch expression&#10;&#10;Simplify the `SmokerStatus.fromCode` method using a switch expression. This improves readability and handles multiple case variations like &quot;y&quot; and &quot;smoker&quot; for SMOKER, defaulting to NON_SMOKER otherwise.">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/insurfact/ins/dto/AdvisorProfileDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/insurfact/ins/dto/AdvisorProfileDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/insurfact/ins/mapper/AdvisorMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/insurfact/ins/mapper/AdvisorMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/insurfact/ins/service/AdvisorService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/insurfact/ins/service/AdvisorService.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;history&quot;: [
    {
      &quot;state&quot;: &quot;OPEN&quot;
    }
  ],
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/hugcanada/ws_dundas.git&quot;,
    &quot;accountId&quot;: &quot;04ba95af-53b8-4473-807b-84d971bfae3c&quot;
  }
}</component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/com/insurfact/Entity-JPA/3.1/Entity-JPA-3.1.jar!/com/insurfact/skynet/entity/Lead.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/com/insurfact/Entity-JPA/3.1/Entity-JPA-3.1.jar!/com/insurfact/skynet/entity/LeadsRelationship.class" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/insurfact/ins/GenericLeads.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="jar://$APPLICATION_HOME_DIR$/plugins/restClient/lib/restClient.jar!/com/intellij/ws/rest/client/requests/collection/post-requests.http" environment="test" />
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="false" />
    <option name="FILTER_DEBUG" value="false" />
    <option name="CUSTOM_FILTER" value="" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
    <option name="showPreview" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2w5OqEELBqHPtBIznREcO6Qpcnw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.generated-requests | #3.executor": "Run",
    "HTTP Request.generated-requests | #4.executor": "Run",
    "JUnit.LeadControllerTest.executor": "Run",
    "Maven.ws_dundas [clean].executor": "Run",
    "Maven.ws_dundas [compile].executor": "Run",
    "Maven.ws_dundas [deploy].executor": "Run",
    "Maven.ws_dundas [package].executor": "Run",
    "Maven.ws_dundas [validate].executor": "Run",
    "Maven.ws_dundas_sp [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Payara Server.Payara 6.26.0.executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "Spring Boot.WsDundasSpApplication.executor": "Run",
    "git-widget-placeholder": "roy/features-create__advisort__profile__codebase",
    "go.import.settings.migrated": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "/Users/<USER>/software/workspace/insurfact/ws_dundas/ws_dundas_sp",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Artifacts",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "reference.settings.ide.settings.notifications",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "oracle"
    ]
  }
}]]></component>
  <component name="RunDashboard">
    <option name="openRunningConfigInTab" value="true" />
  </component>
  <component name="RunManager" selected="Payara Server.Payara 6.26.0">
    <configuration name="generated-requests | #3" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#3" index="3" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #4" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#4" index="4" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="LeadControllerTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ws_dundas_sp" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.insurfact.ins.api.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.insurfact.ins.api" />
      <option name="MAIN_CLASS_NAME" value="com.insurfact.ins.api.LeadControllerTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Payara 6.26.0" type="PayaraServerConfiguration" factoryName="Local" APPLICATION_SERVER_NAME="Payara 6.26.0" ALTERNATIVE_JRE_ENABLED="true" ALTERNATIVE_JRE_PATH="homebrew-21" nameIsGenerated="true">
      <option name="OPEN_IN_BROWSER" value="false" />
      <option name="OPEN_IN_BROWSER_URL" value="http://localhost:8082/ws_dundas-1.2/" />
      <option name="UPDATE_ON_FRAME_DEACTIVATION" value="true" />
      <option name="UPDATE_CLASSES_ON_FRAME_DEACTIVATION" value="true" />
      <deployment>
        <artifact name="ws_dundas_sp:war exploded">
          <settings />
        </artifact>
      </deployment>
      <server-settings>
        <option name="DOMAIN_NAME" value="$PROJECT_DIR$/../../payara6/glassfish/domains/domain2" />
        <option name="PRESERVE" value="false" />
        <option name="COMPATIBILITY" value="false" />
        <option name="VIRTUAL_SERVER" />
        <option name="USERNAME" value="admin" />
        <option name="CREDENTIAL_ALIAS" />
      </server-settings>
      <predefined_log_file enabled="true" id="GlassFish" />
      <RunnerSettings RunnerId="AppServerDebuggerRunner">
        <option name="DEBUG_PORT" value="9010" />
      </RunnerSettings>
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9010" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="AppServerDebuggerRunner">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="ws_dundas_sp:war exploded" />
        </option>
      </method>
    </configuration>
    <configuration default="true" type="PayaraServerConfiguration" factoryName="Local" ALTERNATIVE_JRE_ENABLED="false">
      <deployment />
      <server-settings>
        <option name="DOMAIN_NAME" value="domain1" />
        <option name="PRESERVE" value="false" />
        <option name="COMPATIBILITY" value="false" />
        <option name="VIRTUAL_SERVER" />
        <option name="USERNAME" value="admin" />
      </server-settings>
      <predefined_log_file enabled="true" id="GlassFish" />
      <RunnerSettings RunnerId="AppServerDebuggerRunner">
        <option name="DEBUG_PORT" value="9009" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="AppServerDebuggerRunner">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WsDundasSpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ws_dundas_sp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.insurfact.WsDundasSpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP Request.generated-requests | #4" />
      <item itemvalue="HTTP Request.generated-requests | #3" />
      <item itemvalue="JUnit.LeadControllerTest" />
      <item itemvalue="Payara Server.Payara 6.26.0" />
      <item itemvalue="Spring Boot.WsDundasSpApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.generated-requests | #4" />
        <item itemvalue="HTTP Request.generated-requests | #3" />
        <item itemvalue="JUnit.LeadControllerTest" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a945d9b6-f1e7-445f-ae80-7efecb425d02" name="Changes" comment="" />
      <created>1745328156895</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745328156895</updated>
      <workItem from="1745328158470" duration="47000" />
      <workItem from="1745332873812" duration="2138000" />
      <workItem from="1745462840787" duration="6887000" />
      <workItem from="1745993091868" duration="6767000" />
      <workItem from="1746209298381" duration="82000" />
      <workItem from="1746209391667" duration="42552000" />
      <workItem from="1746471489051" duration="31565000" />
      <workItem from="1747858058716" duration="6000" />
      <workItem from="1748015780961" duration="991000" />
      <workItem from="1749051191231" duration="2987000" />
      <workItem from="1749078207141" duration="6551000" />
      <workItem from="1749093064796" duration="1362000" />
      <workItem from="1749094818543" duration="13728000" />
      <workItem from="1749330634967" duration="107000" />
      <workItem from="1749330773676" duration="25340000" />
    </task>
    <task id="LOCAL-00001" summary="Refactor SmokerStatus code parsing with switch expression&#10;&#10;Simplify the `SmokerStatus.fromCode` method using a switch expression. This improves readability and handles multiple case variations like &quot;y&quot; and &quot;smoker&quot; for SMOKER, defaulting to NON_SMOKER otherwise.">
      <option name="closed" value="true" />
      <created>1746309058590</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1746309058590</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Refactor SmokerStatus code parsing with switch expression&#10;&#10;Simplify the `SmokerStatus.fromCode` method using a switch expression. This improves readability and handles multiple case variations like &quot;y&quot; and &quot;smoker&quot; for SMOKER, defaulting to NON_SMOKER otherwise." />
    <option name="LAST_COMMIT_MESSAGE" value="Refactor SmokerStatus code parsing with switch expression&#10;&#10;Simplify the `SmokerStatus.fromCode` method using a switch expression. This improves readability and handles multiple case variations like &quot;y&quot; and &quot;smoker&quot; for SMOKER, defaulting to NON_SMOKER otherwise." />
  </component>
  <component name="VgoProject">
    <integration-enabled>false</integration-enabled>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="com.insurfact.ins.exception.LeadPersistenceException" package="com.insurfact.ins.exception" />
          <option name="timeStamp" value="2" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>